/*
 * NexusPro Authentication Module
 * AsyncLog STL List Operations Implementation
 * 
 * Original Functions: Multiple STL list operations for AsyncLogInfo
 * Original Addresses: 0x1403C3760 - 0x1403C80C0
 * 
 * Purpose: STL list container operations for AsyncLogInfo storage and manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple STL list functions into single file
 * - Fixed malformed template syntax
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Inserts element into list
 * @param this Pointer to the list container
 * @param _Where Iterator position to insert at
 * @param _Val Value to insert
 * @return Iterator to inserted element
 * 
 * Original Function: ?insert@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C3760
 * 
 * Inserts a new element at the specified position in the list.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall 
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::insert(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Where,
    std::pair<int const,CAsyncLogInfo *> *_Val,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    __int64 *v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-58h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nodeptr v8; // [sp+20h] [bp-38h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v9; // [sp+28h] [bp-30h]@4

    v4 = &v7;
    
    // Initialize debug pattern in local variables
    for (i = 20i64; i; --i) {
        *(_DWORD *)v4 = -858993460;
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    // Buy new node for insertion
    v8 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Buynode(this, _Where->_Ptr, _Where->_Ptr->_Prev);
    
    // Copy construct the value into the new node
    std::pair<int const,CAsyncLogInfo *>::pair(&v8->_Myval, _Val);
    
    // Update list size
    ++this->_Mysize;
    
    // Return iterator to inserted element
    v9 = result;
    result->_Ptr = v8;
    
    return v9;
}

/**
 * @brief Erases element from list
 * @param this Pointer to the list container
 * @param _Where Iterator position to erase
 * @return Iterator to next element
 * 
 * Original Function: ?erase@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C4790
 * 
 * Removes the element at the specified position from the list.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall 
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::erase(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Where,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v6; // [sp+0h] [bp-48h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nodeptr v7; // [sp+20h] [bp-28h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v8; // [sp+28h] [bp-20h]@4

    v3 = &v6;
    
    // Initialize debug pattern in local variables
    for (i = 16i64; i; --i) {
        *(_DWORD *)v3 = -858993460;
        v3 = (__int64 *)((char *)v3 + 4);
    }
    
    v7 = _Where->_Ptr;
    
    // Get iterator to next element
    v8 = result;
    result->_Ptr = v7->_Next;
    
    // Unlink the node from the list
    v7->_Prev->_Next = v7->_Next;
    v7->_Next->_Prev = v7->_Prev;
    
    // Destroy the value in the node
    std::pair<int const,CAsyncLogInfo *>::~pair(&v7->_Myval);
    
    // Free the node
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Freenode(this, v7);
    
    // Update list size
    --this->_Mysize;
    
    return v8;
}

/**
 * @brief Clears all elements from list
 * @param this Pointer to the list container
 * 
 * Original Function: ?clear@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C6250
 * 
 * Removes all elements from the list, leaving it empty.
 */
void __fastcall 
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::clear(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-38h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nodeptr v4; // [sp+20h] [bp-18h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nodeptr v5; // [sp+28h] [bp-10h]@4

    v1 = &v3;
    
    // Initialize debug pattern in local variables
    for (i = 12i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Iterate through all nodes and free them
    v4 = this->_Myhead->_Next;
    while (v4 != this->_Myhead) {
        v5 = v4->_Next;
        
        // Destroy the value in the node
        std::pair<int const,CAsyncLogInfo *>::~pair(&v4->_Myval);
        
        // Free the node
        std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Freenode(this, v4);
        
        v4 = v5;
    }
    
    // Reset head pointers
    this->_Myhead->_Next = this->_Myhead;
    this->_Myhead->_Prev = this->_Myhead;
    this->_Mysize = 0;
}

/**
 * @brief Returns the number of elements in the list
 * @param this Pointer to the list container
 * @return unsigned __int64 Number of elements in the list
 *
 * Original Function: ?size@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C4650
 *
 * Returns the number of elements currently stored in the list.
 */
unsigned __int64 __fastcall
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::size(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this)
{
    return this->_Mysize;
}

/**
 * @brief Returns the maximum possible number of elements
 * @param this Pointer to the list container
 * @return unsigned __int64 Maximum possible number of elements
 *
 * Original Function: ?max_size@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C5F60
 *
 * Returns the maximum number of elements the list can theoretically hold.
 */
unsigned __int64 __fastcall
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::max_size(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this)
{
    // Return maximum size for allocator
    return std::allocator<std::pair<int const,CAsyncLogInfo *>>::max_size(&this->_Myallocator);
}

/**
 * @brief Internal size increment helper
 * @param this Pointer to the list container
 *
 * Original Function: ?_Incsize@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C4D90
 *
 * Internal helper function to increment the list size counter.
 */
void __fastcall
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Incsize(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this,
    unsigned __int64 _Count)
{
    this->_Mysize += _Count;
}

/**
 * @brief Internal insert helper function
 * @param this Pointer to the list container
 * @param _Where Iterator position to insert at
 * @param _Val Value to insert
 * @return Iterator to inserted element
 *
 * Original Function: ?_Insert@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C4670
 *
 * Internal helper function for inserting elements into the list.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Insert(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Where,
    std::pair<int const,CAsyncLogInfo *> *_Val,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    // This is typically just a wrapper to the public insert function
    return std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::insert(this, _Where, _Val, result);
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
